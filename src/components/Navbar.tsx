import { useState } from "react";
import { <PERSON>u, X } from "lucide-react";

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="absolute w-full z-50 py-5" style={{ backgroundColor: 'transparent' }}>
      <div className="container mx-auto px-4 relative">
        {/* Logo - Centered */}
        <div className="flex justify-center mb-8">
          <a href="#" className="flex items-center z-50">
            <img
              src="/lovable-uploads/a3e58cfa-34e3-466c-af1b-f608da9d27e4.png"
              alt="FireFly Burger Logo"
              className="h-32 md:h-40"
            />
          </a>
        </div>

        {/* Desktop Navigation - Top Right */}
        <nav className="hidden md:flex items-center justify-end space-x-8 absolute top-0 right-4">
          <a
            href="#corporate"
            className="text-white text-lg font-medium relative group px-3 py-1 border border-transparent hover:border-white transition-all duration-300 rounded-md"
          >
            <span className="group-hover:text-white transition-colors duration-300">Corporate</span>
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-firefly-red group-hover:w-full transition-all duration-300"></span>
          </a>
          <a
            href="#partner"
            className="text-white text-lg font-medium relative group px-3 py-1 border border-transparent hover:border-white transition-all duration-300 rounded-md"
          >
            <span className="group-hover:text-white transition-colors duration-300">Become a Partner</span>
            <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-firefly-red group-hover:w-full transition-all duration-300"></span>
          </a>
        </nav>

        {/* Mobile Menu Button */}
        <button
          className="md:hidden text-white z-50 absolute top-8 right-4"
          onClick={() => setIsMenuOpen(!isMenuOpen)}
        >
          {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
        </button>

        {/* Mobile Menu */}
        <div
          className={`fixed inset-0 bg-firefly-black bg-opacity-95 flex flex-col items-center justify-center transition-all duration-300
                    ${isMenuOpen ? "opacity-100 visible" : "opacity-0 invisible"}`}
        >
          <nav className="flex flex-col items-center space-y-8 py-8">
            <a
              href="#corporate"
              className="text-white text-2xl font-medium relative group px-4 py-2 border border-transparent hover:border-white transition-all duration-300 rounded-md"
              onClick={() => setIsMenuOpen(false)}
            >
              <span className="group-hover:text-white transition-colors duration-300">Corporate</span>
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-firefly-red group-hover:w-full transition-all duration-300"></span>
            </a>
            <a
              href="#partner"
              className="text-white text-2xl font-medium relative group px-4 py-2 border border-transparent hover:border-white transition-all duration-300 rounded-md"
              onClick={() => setIsMenuOpen(false)}
            >
              <span className="group-hover:text-white transition-colors duration-300">Become a Partner</span>
              <span className="absolute -bottom-1 left-0 w-0 h-0.5 bg-firefly-red group-hover:w-full transition-all duration-300"></span>
            </a>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Navbar;
